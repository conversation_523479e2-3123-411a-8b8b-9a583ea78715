package enums

import "time"

const (
	FileSizeBYTE = 1
	FileSizeKB   = FileSizeBYTE * 1024
	FileSizeMB   = FileSizeKB * 1024
	FileSizeGB   = FileSizeMB * 1024
)

const (
	CompressedFilePath = "user-compressed"
)

const (
	CompressedFileExpire = time.Second * 60 * 60 * 24
)

const (
	FileOptUriSizeBatch        = "/file/size_batch"
	FileOptUriCompress         = "/file/compress"
	FileOptUriCompressProgress = "/file/compress_progress"
)

type ModelSource uint

const (
	ModelSourceHuggingFace ModelSource = iota + 1
	ModelSourceCivitai
)

const (
	FlashUploadFileExist = iota + 1
	FlashUploadFileNotExist
)

type DirShareType uint

// ThirdModelDownloadStatus 第三方模型下载任务状态枚举
type thirdModelDownloadStatusEnum_ struct {
	Completed  uint // 下载完成
	Failed     uint // 下载失败
	Downloading uint // 下载中
}

var ThirdModelDownloadStatusEnum = thirdModelDownloadStatusEnum_{
	Completed:   1, // 下载完成
	Failed:      2, // 下载失败
	Downloading: 3, // 下载中
}

// GetStatusName 获取状态名称
func (t thirdModelDownloadStatusEnum_) GetStatusName(status uint) string {
	switch status {
	case t.Completed:
		return "下载完成"
	case t.Failed:
		return "下载失败"
	case t.Downloading:
		return "下载中"
	default:
		return "未知状态"
	}
}
