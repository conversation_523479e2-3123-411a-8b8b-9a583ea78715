package model

import "gorm.io/gorm"

type ThirdModelDownloadTask struct {
	gorm.Model
	TaskUuid         string `json:"task_uuid" gorm:"type:varchar(50);not null;default:'';comment:任务uuid"`
	UserId           uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	PodUuid          string `json:"pod_uuid" gorm:"type:varchar(50);not null;default:'';comment:pod uuid"`
	DownloadUrl      string `json:"download_url" gorm:"type:varchar(200);not null;default:'';comment:下载地址"`
	DownloadPath     string `json:"download_path" gorm:"type:varchar(200);not null;default:'';comment:下载路径"`
	DownloadFileSize int64  `json:"download_file_size" gorm:"type:bigint;not null;default:0;comment:下载文件大小"`
	Status           uint   `json:"status" gorm:"type:int;not null;default:0;comment:状态 1.下载完成 2.下载失败 3.下载中"`
	Msg              string `json:"msg" gorm:"type:varchar(200);not null;default:'';comment:状态信息"`
}
