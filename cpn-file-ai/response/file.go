package response

type CompressFileResp struct {
	FileUuid string `json:"file_uuid"`
}

type GetFileCompressProgressResp struct {
	CompressPercentage uint `json:"compress_percentage"`
}

type FlashUploadCheckResp struct {
	Exist uint `json:"exist"` // 文件是否存在  1.存在  2.不存在
}

type DownloadTaskItem struct {
	ID               uint   `json:"id"`
	TaskUuid         string `json:"task_uuid"`
	DownloadUrl      string `json:"download_url"`
	DownloadPath     string `json:"download_path"`
	DownloadFileSize int64  `json:"download_file_size"`
	Status           uint   `json:"status"`
	Msg              string `json:"msg"`
	CreatedAt        string `json:"created_at"`
	UpdatedAt        string `json:"updated_at"`
}

type GetDownloadTasksResp struct {
	Tasks []DownloadTaskItem `json:"tasks"`
}
