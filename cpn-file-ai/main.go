package main

import (
	"cpn-file-ai/common/constants"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/redis-client"
	"cpn-file-ai/common/sysLogger"
	"cpn-file-ai/config"
	"cpn-file-ai/middleware"
	"cpn-file-ai/model"
	"cpn-file-ai/router"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"os"
)

func main() {
	logger.Info("启动main " + constants.Version)
	sysLogger.SetupLogger()
	sysLogger.SysLog("Cpn Ai " + constants.Version + " started")
	if os.Getenv("GIN_MODE") != "debug" {
		gin.SetMode(gin.ReleaseMode)
	}
	if constants.DebugEnabled {
		sysLogger.SysLog("running in debug mode")
	}
	// Initialize SQL Database
	if err := model.InitDB(); err != nil {
		sysLogger.FatalLog("failed to initialize database: " + err.Error())
	}
	defer func() {
		err := model.CloseDB()
		if err != nil {
			sysLogger.FatalLog("failed to close database: " + err.Error())
		}
	}()

	// Initialize Redis
	if err := redis_client.InitRedisClient(); err != nil {
		sysLogger.FatalLog("failed to initialize Redis: " + err.Error())
	}

	// Initialize HTTP server
	server := gin.New()
	server.Use(gin.Recovery())
	// This will cause SSE not to work!!!
	server.Use(middleware.RequestId())
	middleware.SetUpLogger(server)
	// Initialize session store
	store := cookie.NewStore([]byte(constants.SessionSecret))
	server.Use(sessions.Sessions("session", store))
	router.SetApiRouter(server)
	sysLogger.SysLog("开启端口监听..." + config.HttpPort)
	logger.Info("开启端口监听..." + config.HttpPort)
	if err := server.Run(config.HttpPort); err != nil {
		logger.Info("开启端口监听失败 port"+config.HttpPort, " err:", err.Error())
		sysLogger.FatalLog("failed to start HTTP server: " + err.Error())
	}
}
