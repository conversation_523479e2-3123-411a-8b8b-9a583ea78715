package controller

import (
	"cpn-file-ai/common/constants"
	"cpn-file-ai/common/jsontime"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/redis-client"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/enums"
	"cpn-file-ai/middleware"
	"cpn-file-ai/model"
	"cpn-file-ai/request"
	"cpn-file-ai/response"
	"cpn-file-ai/service"
	"cpn-file-ai/structs"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

type filesApi_ struct {
	Mergeing map[string]int64
}

var FilesApi = filesApi_{
	///Mergeing: make(map[string]int64),
}

func (obj filesApi_) Files(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	action := ""
	catalogue := ""
	redirect := ""
	podUuid := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if action == "Download" && code == 0 {

		} else {
			//if action == "GetDir" {
			//	logger.Info("设置Cookie:", c.Request.Header.Get("Authorization"))
			//
			//	key := c.Request.Header.Get("Authorization")
			//	if key == "" {
			//		key = "string is empty in file"
			//	}
			//	c.SetCookie("Authorization", key, 3*86400, "/", ".chenyu.cn", false, false)
			//
			//}
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	//claims.UserId = 1202

	var oReq request.FilesReq

	if c.Request.Method == http.MethodPost {
		action, _ = c.GetPostForm("action")
		catalogue, _ = c.GetPostForm("catalogue")
		redirect, _ = c.GetPostForm("redirect")
		podUuid, _ = c.GetPostForm("pod_uuid")
	} else if c.Request.Method == http.MethodGet {
		action, _ = c.GetQuery("action")
		catalogue, _ = c.GetQuery("catalogue")
		redirect, _ = c.GetQuery("redirect")
		podUuid, _ = c.GetQuery("pod_uuid")
	} else {
		msg = "请求方式不正确"
		return
	}

	if action == "Download" && c.Request.Method == http.MethodGet {
		oReq.Action = action
		oReq.Path, _ = c.GetQuery("path")
		if oReq.Path == "" {
			msg = "下载路径不正确"
			return
		}
		oReq.Catalogue = catalogue
		oReq.Redirect = redirect
		oReq.PodUuid = podUuid
	} else if action == "ChunkUpload" || action == "Upload" || action == "Download" {
		oReq.Action = action
		oReq.Path, _ = c.GetPostForm("path")
		oReq.Catalogue, _ = c.GetPostForm("catalogue")
		oReq.Redirect, _ = c.GetPostForm("redirect")
		oReq.PodUuid, _ = c.GetPostForm("pod_uuid")
	} else {
		if err := c.ShouldBindJSON(&oReq); err != nil {
			msg = "参数解析失败"
			logger.Error(msg, err)
			return
		}
	}
	oReq.Path = strings.TrimLeft(oReq.Path, "/")
	oReq.NewName = strings.TrimLeft(oReq.NewName, "/")
	oReq.NewPath = strings.TrimLeft(oReq.NewPath, "/")

	if err := utils.IsValidPathOrName([]string{oReq.Path, oReq.NewPath, oReq.NewName, oReq.Filename}); err != nil {
		logger.Error(err)
		msg = "路径非法"
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
		return
	}
	userBasePath, err := service.FileService.PreCheckUserStorage(user)
	if err != nil {
		msg = err.Error()
		return
	}
	bigUserBasePath := userBasePath

	if oReq.Catalogue != "" {
		if redirectPath, basePath, err := service.UserCatalogue.GetPathOfHost(oReq.Catalogue, claims.UserId, oReq.PodUuid); err != nil {
			msg = "获取路径失败"
			logger.Error(msg, err)
			return
		} else {
			pre := fmt.Sprintf("redirectPath:%s  basePath:%s ", redirectPath, basePath)
			logger.Info(pre)
			if oReq.PodUuid != "" {
				var pod model.Pod
				if err = pod.GetByUuid(oReq.PodUuid); err != nil {
					msg = "查询Pod信息失败"
					logger.Error(pre, msg, err, "podUuid:", oReq.PodUuid)
					return
				}
				// 额外判断实例是否共享输入/输出目录，不共享则拼上实例uuid
				catalogueItem := strings.Split(pod.Catalogue, ",")
				switch oReq.Catalogue {
				case service.DirKeyComfyAppdataInput, service.DirKeySdAppdataInput, service.DirKeyForgeAppdataInput:
					logger.Info(fmt.Sprintf("redirectPath:%s  basePath:%s ", redirectPath, basePath))
					if slices.Contains(catalogueItem, service.DirKeyUnShareInput) {
						redirectPath = path.Join(redirectPath, oReq.InstanceUuid)
					}
				case service.DirKeyComfyAppdataOutput, service.DirKeySdAppdataOutput, service.DirKeyForgeAppdataOutput:
					if slices.Contains(catalogueItem, service.DirKeyUnShareOutput) {
						redirectPath = path.Join(redirectPath, oReq.InstanceUuid)
					}
				}
				if power, err := service.UserCatalogue.GetNeedPrivilege(oReq.Catalogue); err != nil {
					msg = "获取权限信息失败"
					logger.Error(pre, msg, err)
					return
				} else {
					if power == enums.CataLoguePrivilegeEnum.USER {

					} else if power == enums.CataLoguePrivilegeEnum.KOL {
						if pod.UserId != claims.UserId {
							msg = "无权限"
							logger.Error(pre, msg)
							return
						}
					} else if power == enums.CataLoguePrivilegeEnum.SYS {
						msg = "无权限"
						logger.Error(pre, msg)
						return
					} else {
						msg = "无权限"
						logger.Error(pre, msg)
						return
					}
				}
			}

			userBasePath = basePath
			if oReq.Redirect == "t" {
				oReq.Path = redirectPath
			}
		}
	}

	if service.UserCatalogue.CheckBasePath(userBasePath) == false {
		msg = "根目录不正确"
		logger.Error(msg, "userId:", claims.UserId, "  userBasePath:", userBasePath)
		return
	}

	action = oReq.Action
	if action == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}
	if action == "DownloadKey" {
		//uuid := utils.GetUUID()
		//redisKey := enums.RedisKeyEnum.LockKey + uuid
		//if err := common.RedisSet(redisKey, c.Request.Header.Get("Authorization"), time.Second*60); err != nil {
		//	logger.Error(err)
		//	msg = "获取下载码失败"
		//	return
		//} else {
		//	result["download_key"] = uuid
		//	result["root_path"] = oReq.Path
		//	code = 0
		//}
	} else if action == "ChunkVerify" {

		filename := oReq.Filename
		if filename == "" {
			msg = "文件名参数为空"
			return
		}

		chunkMap := make(map[int]fileInfo)
		chunkFileTempDir := filepath.Join(userBasePath, "chunk", filename)
		if _, err := os.Stat(chunkFileTempDir); err != nil {
			if os.IsNotExist(err) {
				result["chunks"] = chunkMap
				code = 0
				return
			} else {
				msg = "临时文件夹路径检测错误"
				logger.Error(msg, err)
				return
			}
		}

		if arr, err := readDir(chunkFileTempDir); err != nil {
			msg = "列出文件出错"
			if os.IsNotExist(err) {
				msg = "文件或者目录不存在"
			}
			logger.Error(msg, err, " chunkFileTempDir:", chunkFileTempDir)
			return
		} else {
			maxChunkIndex := 0 //去除最后一个文件，让用户重新上传触发文件合并
			for _, item := range arr {
				if item.IsDir == false && strings.Contains(item.Name, filename) {
					tmpAry := strings.Split(item.Name, "_")
					if len(tmpAry) >= 2 {
						if chunkIndex, err := strconv.Atoi(tmpAry[len(tmpAry)-1]); err == nil {
							if chunkIndex > maxChunkIndex {
								maxChunkIndex = chunkIndex
							}
							chunkMap[chunkIndex] = item
						}
					}
				}
			}
			delete(chunkMap, maxChunkIndex)
			result["chunks"] = chunkMap
			code = 0
			return
		}

	} else if action == "ChunkUpload" {
		if service.OutOfAmount(claims.UserId) {
			msg = "账户已欠费，请先充值再使用该功能"
			logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
			code = 5
			return
		}
		ary := strings.Split(oReq.Path, "/")
		if len(ary) > 50 {
			msg = "不支持自动创建三级结构目录"
			logger.Error(msg, oReq, claims.UserId)
			return
		}

		mergeTest, _ := c.GetPostForm("merge_test")

		// 分片序号
		chunkIndexStr, _ := c.GetPostForm("chunk_index")
		if chunkIndexStr == "" {
			msg = "分片序号不正确"
			return
		}
		chunkIndex, _ := strconv.Atoi(chunkIndexStr)
		if chunkIndex < 0 {
			msg = "分片序号不正确"
			return
		}
		result["chunk_index"] = chunkIndex

		//分片总数
		chunkTotalStr, _ := c.GetPostForm("chunk_total")
		chunkTotal, _ := strconv.Atoi(chunkTotalStr)
		if chunkTotal <= 0 {
			msg = "分片总数不正确"
			return
		}

		// 文件总大小
		sizeTotalStr, _ := c.GetPostForm("size_total")
		sizeTotal, _ := strconv.Atoi(sizeTotalStr)
		if sizeTotal <= 0 {
			msg = "文件总大小不正确"
			return
		}

		f, errf := c.FormFile("file")
		if errf != nil {
			msg = "文件上传失败"
			logger.Error(msg, errf)
			return
		}

		if f.Filename == "" {
			msg = "文件名不能为空"
			return
		}

		chunkFileTempDir := filepath.Join(userBasePath, "chunk", f.Filename)
		if _, err := os.Stat(chunkFileTempDir); err != nil {
			if os.IsNotExist(err) {
				if err := os.MkdirAll(chunkFileTempDir, os.ModePerm); err != nil {
					msg = "创建临时文件夹出错"
					logger.Error(msg, err, oReq)
					return
				}
			} else {
				msg = "临时文件夹路径检测错误"
				logger.Error(msg, err)
				return
			}
		}

		rootPath := filepath.Join(userBasePath, oReq.Path)
		if _, err := os.Stat(rootPath); err != nil {
			if os.IsNotExist(err) {
				if err := os.MkdirAll(rootPath, os.ModePerm); err != nil {
					msg = "创建文件夹出错"
					logger.Error(msg, err, oReq)
					return
				}
			} else {
				msg = "路径检测错误"
				logger.Error(msg, err)
				return
			}
		}
		saveFilePath := filepath.Join(rootPath, f.Filename)

		chunkFilePath := filepath.Join(chunkFileTempDir, f.Filename+"_"+chunkIndexStr)
		if err := c.SaveUploadedFile(f, chunkFilePath); err != nil {
			msg = "文件保存失败"
			logger.Error(msg, err)
			return
		} else {
			var totalChunkSize int64
			for i := 0; i < chunkTotal; i++ {
				iStr := strconv.Itoa(i)
				// 分片大小获取
				tmpChunkFilePath := filepath.Join(chunkFileTempDir, f.Filename+"_"+iStr)
				if fi, err := os.Stat(tmpChunkFilePath); err != nil {
					if os.IsNotExist(err) {

					} else {
						logger.Error(err, " tmpChunkFilePath:", tmpChunkFilePath)
					}
					break
				} else {
					totalChunkSize += fi.Size()
				}
			}
			if totalChunkSize == int64(sizeTotal) { //开始合并
				logger.Info("开始合并", chunkFileTempDir)
				md5 := utils.GetMd5(saveFilePath)
				lockKey := enums.RedisKeyEnum.LockKey + "MergeFile_" + md5
				if _, ok := service.TaskRunning.Load(md5); !ok {
					if redis_client.RedisLock(lockKey, 1, 0) {
						defer redis_client.RedisUnLock(lockKey)
						if _, ok := service.TaskRunning.Load(md5); !ok {
							if mergeTest != "" {
								msg = "合并文件失败 mergeTest测试"
								return
							}
							if err := mergeFile(chunkTotal, f.Filename, chunkFileTempDir, saveFilePath); err != nil {
								logger.Error(err, "  合并文件失败", chunkTotal, " ", f.Filename, " ", chunkFileTempDir, " ", saveFilePath)
								msg = "合并文件失败"
								return
							} else {
								msg = "文件上传成功"
								code = 0
								return
							}
						}
					} else {
						logger.Info("锁定中：", lockKey, "  fileName:", f.Filename)
					}
				}
			}
			msg = "文件分片上传成功"
			code = 0
			return
		}
	} else if action == "Upload" {
		if service.OutOfAmount(claims.UserId) {
			msg = "账户已欠费，请先充值再使用该功能"
			logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
			code = 5
			return
		}
		ary := strings.Split(oReq.Path, "/")
		if len(ary) > 50 {
			msg = "不支持自动创建三级结构目录"
			logger.Error(msg, oReq, claims.UserId)
			return
		}

		f, errf := c.FormFile("file")
		if errf != nil {
			msg = "文件上传失败"
			logger.Error(msg, errf)
			return
		}
		rootPath := filepath.Join(userBasePath, oReq.Path)
		if _, err := os.Stat(rootPath); err != nil {
			if os.IsNotExist(err) {
				if err := os.MkdirAll(rootPath, os.ModePerm); err != nil {
					msg = "创建文件夹出错"
					logger.Error(msg, err, oReq)
					return
				}
			} else {
				msg = "路径检测错误"
				logger.Error(msg, err)
				return
			}
		}
		filePath := filepath.Join(rootPath, f.Filename)
		if _, err := os.Stat(filePath); err != nil {
			if os.IsNotExist(err) {
			} else {
				msg = "路径检测错误"
				logger.Error(msg, err)
				return
			}
		} else {
			msg = "文件已存在，上传失败"
			logger.Error(msg, oReq, claims.UserId)
			return
		}
		if err := c.SaveUploadedFile(f, filePath); err != nil {
			msg = "文件保存失败"
			logger.Error(msg, err)
			return
		} else {
			msg = "文件上传成功"
			code = 0
			return
		}
	} else if action == "Download" && c.Request.Method == http.MethodGet {
		if service.OutOfAmount(claims.UserId) {
			msg = "账户已欠费，请先充值再使用该功能"
			logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
			code = 5
			return
		}
		rootPath := filepath.Join(userBasePath, oReq.Path)
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			baseName := path.Base(rootPath)
			// 将文件内容返回给用户进行下载
			//encodedFileName := url.QueryEscape(baseName)

			// 设置响应头，通知浏览器这是文件下载
			c.Header("Content-Disposition", "attachment; filename="+baseName)
			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Length", strconv.FormatInt(info.Size(), 10))

			file, err1 := os.Open(rootPath)
			if err1 != nil {
				msg = "打开文件失败"
				logger.Error(msg, " err:", err1)
				return
			}
			defer file.Close()
			// 返回文件流
			http.ServeContent(c.Writer, c.Request, info.Name(), info.ModTime(), file)
			code = 0
			//
			//c.Header("Content-Length", strconv.FormatInt(info.Size(), 10))
			//c.Header("Content-Encoding", "gzip")
			//c.Header("Content-Disposition", "attachment; filename="+baseName)
			////c.Header("Content-Type", "application/octet-stream")
			//code = 0
			//c.File(rootPath)
			return
		} else if info.Mode().IsDir() {
			msg = "文件路径错误"
			logger.Error(msg, oReq)
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}

	} else if action == "Download" {
		if service.OutOfAmount(claims.UserId) {
			msg = "账户已欠费，请先充值再使用该功能"
			logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
			code = 5
			return
		}
		rootPath := filepath.Join(userBasePath, oReq.Path)
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			baseName := path.Base(rootPath)
			// 将文件内容返回给用户进行下载
			//encodedFileName := url.QueryEscape(baseName)

			c.Header("Content-Length", strconv.FormatInt(info.Size(), 10))
			c.Header("Content-Encoding", "gzip")
			c.Header("Content-Disposition", "attachment; filename="+baseName)
			//c.Header("Content-Type", "application/octet-stream")
			code = 0
			c.File(rootPath)
			return
		} else if info.Mode().IsDir() {
			msg = "文件路径错误"
			logger.Error(msg, oReq)
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}

	} else if action == "GetSize" {
		logger.Info("action:", action)
		rootPath := filepath.Join(bigUserBasePath, oReq.Path)
		if oReq.Redirect == "t" {
			rootPath = bigUserBasePath
			oReq.Path = ""
		}
		logger.Info("GetSize rootPath:", rootPath)
		result["free_size"] = constants.CloudStoreFreeSize
		result["limit_size"] = constants.CloudStoreLimitSize
		result["hour_price"] = constants.CloudStoreHourPrice
		result["root_path"] = oReq.Path

		// 是否是查询用户目录
		isUserSubPath := false
		if strings.HasPrefix(strings.TrimRight(rootPath, "/"), strings.TrimRight(bigUserBasePath, "/")) {
			isUserSubPath = true
		}

		// 是否是查询用户主目录
		isUserBasePath := false
		if strings.TrimRight(rootPath, "/") == strings.TrimRight(bigUserBasePath, "/") {
			isUserBasePath = true
		}

		// 如果是用户主目录，大小先设置为上一个小时大小
		if isUserBasePath {
			result["sum_size"] = user.PrivateSize
		}
		logger.Info("utils.CalculateSizeWithTimeout 开始")

		// 如果用户目录，走api获取数据
		if isUserSubPath {
			if size, err := service.FileService.GetDirSizeRemote(user.ID, rootPath); err != nil {
				logger.Error(err, "userId:", user.ID)
				msg = "获取文件大小失败"
				logger.Error("utils.CalculateSizeWithTimeout 出错 userID:", user.ID)
				return
			} else {
				logger.Info("service.GetDirSize 完成")
				result["sum_size"] = size
				// 如果是用户主目，更新用户信息
				if isUserBasePath {
					beforeSize := user.PrivateSize
					if err1 := user.UpdatePrivateSize(user.ID, size); err1 != nil {
						logger.Error(err1, "userId:", user.ID)
					}
					service.WarnService.PrivateCloudStore(user.ID, beforeSize, size)
				}
			}
		} else {
			if size1, err1 := service.GetDirSize(user.ID, rootPath); err1 != nil {
				logger.Error(err1, "userId:", user.ID)
				msg = "获取大小失败"
				if size1 == -2 {
					msg = "正在统计"
				}
				result["sum_size"] = size1
				return
			} else {
				//logger.Info("service.GetDirSize 完成")
				result["sum_size"] = size1
			}
		}

		code = 0
		return
	} else if action == "GetDir" {
		rootPath := filepath.Join(userBasePath, oReq.Path)
		arr := make([]fileInfo, 0)
		var err error
		isUserSubPath := false
		if strings.HasPrefix(strings.TrimRight(rootPath, "/"), strings.TrimRight(bigUserBasePath, "/")) {
			isUserSubPath = true
		}
		// userdata通过api读取，其他的还是走正常的读取
		if isUserSubPath {
			arr, err = readDirRemote(rootPath)
		} else {
			arr, err = readDir(rootPath)
		}

		if err != nil {
			result["root_path"] = oReq.Path
			msg = "列出文件出错"
			if os.IsNotExist(err) {
				msg = "文件或者目录不存在"
			}
			logger.Error(msg, err, " rootPath:", rootPath)
			return
		} else {
			fmt.Println(rootPath, "    ", userBasePath, "  oReq.Path:", oReq.Path)
			//result["root_path"] = strings.Replace(rootPath, userBasePath, "", -1)
			result["root_path"] = oReq.Path
			result["dir"] = arr
			code = 0
			//c.SetCookie("Authorization", c.Request.Header.Get("Authorization"), 7*86400, "/")
			// Set Cookie with Domain .chenyu.cn to share across subdomains
			//c.SetCookie("Authorization", c.Request.Header.Get("Authorization"), 3*86400, "/", ".chenyu.cn", false, false)

			return
		}
	} else if action == "DeleteFile" || action == "DeleteDir" {
		rootPath := filepath.Join(userBasePath, oReq.Path)
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, rootPath, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, rootPath, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			if action != "DeleteFile" {
				msg = "请选择要删除的文件"
				return
			}
			if err = os.Remove(rootPath); err != nil {
				msg = "删除文件出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件删除成功"
			code = 0
			return
		} else if info.Mode().IsDir() {
			if action != "DeleteDir" {
				msg = "请选择要删除的文件夹"
				return
			}
			if err = os.RemoveAll(rootPath); err != nil {
				msg = "删除文件夹出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件夹删除成功"
			code = 0
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}
	} else if action == "Rename" {
		if oReq.NewName == "" {
			msg = "请输出新的名称"
			return
		}
		rootPath := filepath.Join(userBasePath, oReq.Path)
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "文件不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取文件信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		dir := filepath.Dir(rootPath)
		newPath := filepath.Join(dir, oReq.NewName)

		// 检查文件类型
		if info.Mode().IsRegular() {
			if err = os.Rename(rootPath, newPath); err != nil {
				msg = "文件重命名出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件重命名成功"
			code = 0
			return
		} else if info.Mode().IsDir() {
			if err = os.Rename(rootPath, newPath); err != nil {
				msg = "文件夹重命名出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件夹重命名成功"
			code = 0
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}
	} else if action == "CreateDir" {
		rootPath := filepath.Join(userBasePath, oReq.Path)
		_, err := os.Stat(rootPath)
		if err == nil {
			msg = "路径已存在"
			logger.Error(msg, oReq)
			return
		} else {
			if os.IsNotExist(err) {
				msg = "路径不存在"
			} else {
				msg = "获取信息出错"
				logger.Error(msg, oReq)
				return
			}
		}

		if err := os.MkdirAll(rootPath, os.ModePerm); err != nil {
			msg = "创建文件夹出错"
			logger.Error(msg, err, oReq)
			return
		} else {
			msg = "文件夹创建成功"
			code = 0
			return
		}

	} else if action == "CreateFile" {
		rootPath := filepath.Join(userBasePath, oReq.Path)
		_, err := os.Stat(rootPath)
		if err == nil {
			msg = "文件路径已存在"
			logger.Error(msg, oReq)
			return
		} else {
			if os.IsNotExist(err) {
				msg = "路径不存在"
			} else {
				msg = "获取信息出错"
				logger.Error(msg, oReq)
				return
			}
		}

		file, err := os.Create(rootPath)
		defer file.Close()
		if err != nil {
			msg = "创建文件出错"
			logger.Error(msg, err, oReq)
			return
		} else {
			msg = "文件创建成功"
			code = 0
			return
		}
	} else if action == "Move" {
		rootPath := filepath.Join(userBasePath, oReq.Path)
		targetPath := filepath.Join(userBasePath, oReq.NewPath)

		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			baseFileName := path.Base(rootPath)
			targetFilePath := filepath.Join(targetPath, baseFileName)
			if err = os.Rename(rootPath, targetFilePath); err != nil {
				msg = "文件移动出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件移动成功"
			code = 0
			return
		} else if info.Mode().IsDir() {
			dirName := filepath.Base(rootPath)
			targetDirPath := filepath.Join(targetPath, dirName)
			if err = os.Rename(rootPath, targetDirPath); err != nil {
				msg = "文件夹移动出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件夹移动成功"
			code = 0
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}
	}

	msg = ""
	code = 0
}

func (obj filesApi_) FileCompress(c *gin.Context) {
	var (
		err    error
		req    request.CompressFileReq
		result response.CompressFileResp
	)

	code := 1
	msg := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	if err = c.BindJSON(&req); err != nil {
		logger.Error("参数错误", err)
		msg = "参数有误"
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	result, err = service.FileService.CompressFile(req, claims.UserId)
	if err != nil {
		msg = err.Error()
		logger.Error(msg, err)
		return
	}

	msg = "开始压缩"
	code = 0
	return
}

func (obj filesApi_) GetFileCompressProgress(c *gin.Context) {
	var (
		err  error
		req  request.GetFileCompressProgressReq
		resp response.GetFileCompressProgressResp
	)

	code := 1
	msg := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": resp,
		})
	}()

	if err = c.BindQuery(&req); err != nil {
		logger.Error("参数错误", err)
		msg = "参数有误"
		return
	}

	if req.FileUuid == "" {
		msg = "参数有误"
		return
	}
	percentage, err := service.FileService.GetFileCompressProgress(req)
	if err != nil {
		msg = "获取压缩进度失败"
		logger.Error(msg, err)
		return
	}
	resp.CompressPercentage = percentage
	code = 0
	return
}

func (obj filesApi_) DownloadCompressedFile(c *gin.Context) {
	code := 1
	msg := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}()

	var (
		err error
		req request.FileCompressDownloadReq
	)

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	if err = c.BindQuery(&req); err != nil {
		logger.Error("参数错误", err)
		msg = "参数有误"
		return
	}

	err = service.FileService.DownloadCompressedFile(c, req, claims.UserId)
	if err != nil {
		msg = err.Error()
		return
	}
	code = 0
	return
}

func (obj filesApi_) DownloadModelBackground(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var (
		err error
		req request.ThirdModelDownloadReq
	)
	if err = c.BindJSON(&req); err != nil {
		msg = "参数解析失败"
		code = 1
		logger.Error(msg, err)
		return
	}

	msg, code = obj.checkModelDownloadParams(req)
	if code != 0 {
		return
	}

	err = service.FileService.DownloadThirdModel(req, claims.UserId)
	if err != nil {
		msg = err.Error()
		code = 1
		return
	}
	msg = "开始下载"
	return
}

func (obj filesApi_) GetDownloadTasks(c *gin.Context) {
	code := 1
	msg := ""
	var result interface{}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var (
		err error
		req request.GetDownloadTasksReq
	)
	if err = c.BindJSON(&req); err != nil {
		msg = "参数解析失败"
		code = 1
		logger.Error(msg, err)
		return
	}

	result, err = service.FileService.GetDownloadTasks(req, claims.UserId)
	if err != nil {
		msg = err.Error()
		code = 1
		return
	}

	msg = "查询成功"
	code = 0
	return
}

func (obj filesApi_) FlashUploadCheck(c *gin.Context) {
	code := 1
	msg := ""
	var result interface{}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var (
		err error
		req request.FlashUploadCheckReq
	)
	if err = c.BindJSON(&req); err != nil {
		msg = "参数有误"
		code = 1
		logger.Error(msg, err)
		return
	}

	result, err = service.FileService.FlashUploadCheck(req)
	if err != nil {
		logger.Error(fmt.Sprintf("判断文件hash失败，err: %v", err))
		return
	}
	code = 0
	return
}

func (obj filesApi_) FlashUpload(c *gin.Context) {
	code := 1
	msg := ""
	var result interface{}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("panic:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var (
		err error
		req request.FlashUploadReq
	)
	if err = c.BindJSON(&req); err != nil {
		msg = "参数解析失败"
		code = 1
		logger.Error(msg, err)
		return
	}

	if err = service.FileService.FlashUpload(req, claims.UserId); err != nil {
		msg = "文件上传失败"
	} else {
		msg = "文件上传成功"
		code = 0
	}
	return
}

func (obj filesApi_) checkModelDownloadParams(req request.ThirdModelDownloadReq) (string, int) {
	if req.PodUuid == "" {
		return "请指定pod应用", 1
	}
	if req.ModelSource != enums.ModelSourceHuggingFace && req.ModelSource != enums.ModelSourceCivitai {
		return "不支持的模型来源", 1
	}
	if req.ModelDownloadUrl == "" {
		return "请输入模型下载地址", 1
	}
	return "", 0
}

type fileInfo struct {
	Path    string            `json:"path"`
	Name    string            `json:"name"`
	Size    int64             `json:"size"`
	IsDir   bool              `json:"is_dir"`
	ModTime jsontime.JsonTime `json:"mod_time"` //修改时间
	Mode    uint32            `json:"mode"`
}

type DirResponse struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		Dir []fileInfo `json:"dir"`
	}
}

func readDirRemote(rootPath string) ([]fileInfo, error) {
	reqBody := make(map[string]string)
	reqBody["base_path"] = rootPath
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return nil, err
	}
	var result DirResponse
	err = service.PostFileStorage("/file/dir_list_with_size", jsonData, &result, 30)
	if err != nil {
		return nil, err
	}

	if result.Code != 0 {
		return nil, errors.New(result.Msg)
	}

	logger.Info("文件大小统计完成", rootPath)
	return result.Result.Dir, err

}

func readDir(rootPath string) ([]fileInfo, error) {
	arr := make([]fileInfo, 0)
	if dirEntry, err := os.ReadDir(rootPath); err != nil {
		logger.Error(err, "  rootPath:", rootPath)
		return arr, err
	} else {
		for i := 0; i < len(dirEntry); i++ {
			file := dirEntry[i]
			info, _ := file.Info()
			path := ""
			tmp := fileInfo{
				Path:    path,
				Name:    file.Name(),
				Size:    info.Size(),
				IsDir:   file.IsDir(),
				ModTime: jsontime.JsonTime(info.ModTime()),
				Mode:    uint32(file.Type()),
			}
			arr = append(arr, tmp)
		}
	}
	return arr, nil
}

// 计算文件或文件夹大小
func calculateSize(path string) int64 {
	var size int64

	// 获取文件或文件夹信息
	fileInfo1, err := os.Stat(path)
	if err != nil {
		fmt.Println("无法获取文件或文件夹信息:", err)
		return 0
	}

	// 如果是文件，直接返回文件大小
	if !fileInfo1.IsDir() {
		return fileInfo1.Size()
	}

	// 如果是文件夹，遍历文件夹中的所有文件和子文件夹，并累加大小
	filepath.Walk(path, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			fmt.Printf("无法访问文件或文件夹 %q: %v\n", filePath, err)
			return nil
		}
		// 跳过目录本身
		if filePath != path {
			size += info.Size()
		}
		return nil
	})

	return size
}

func walkDir(root string) ([]fileInfo, error) {
	// 使用 Walk 函数遍历文件夹下的所有文件和目录
	arr := make([]fileInfo, 0)
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Error(err)
			return err
		}
		tmp := fileInfo{
			Path:    path,
			Name:    info.Name(),
			Size:    info.Size(),
			IsDir:   info.IsDir(),
			ModTime: jsontime.JsonTime(info.ModTime()),
			Mode:    uint32(info.Mode()),
		}
		arr = append(arr, tmp)
		// 打印文件或目录的路径
		fmt.Println(path)
		return nil
	})
	if err != nil {
		logger.Error(err)
		return arr, fmt.Errorf("walkDir: %v", err)
	}
	return arr, nil
}

func writeBlock(file *os.File, chunkIndex int, chunkSize int64, fileName string, tmpFileDir string, wg *sync.WaitGroup) error {
	defer wg.Done()
	wg.Add(1)
	chunkFilePath := path.Join(tmpFileDir, fileName+"_"+strconv.Itoa(chunkIndex))
	logger.Info("分片路径:", chunkFilePath)

	// 打开分片文件
	chunkFileObj, err := os.Open(chunkFilePath)
	if err != nil {
		logger.Error("打开分片文件失败:", err, "  ", chunkFilePath)
		return err
	}
	defer chunkFileObj.Close()

	// 计算实际读取的数据大小
	stat, err := chunkFileObj.Stat()
	if err != nil {
		logger.Error("获取分片文件信息失败:", err, "  ", chunkFilePath)
		return err
	}
	chunkFileSize := stat.Size()

	// 读取分片文件的数据
	data := make([]byte, chunkFileSize)
	bytesRead, err := chunkFileObj.Read(data)
	if err != nil {
		logger.Error("读取分片文件失败:", err, "  ", chunkFilePath)
		return err
	}

	// 将文件指针移动到指定偏移位置
	offset := int64(chunkIndex) * chunkSize
	if _, err := file.WriteAt(data[:bytesRead], offset); err != nil {
		logger.Error("写入文件失败: %v\n", err, "  ", chunkFilePath)
		return err
	}
	logger.Info(fmt.Sprintf("fileName:%s chunkIndex:%d, 偏移位置 %d 写入完成", fileName, chunkIndex, offset))
	return nil
}

// 合并切片文件
func mergeFile(chunkTotal int, fileName, tmpFileDir string, saveFilePath string) error {
	commandKey := utils.GetMd5(saveFilePath)
	atomic.AddInt32(&service.TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "合并文件", Msg: tmpFileDir, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	service.TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&service.TaskRunningCount, -1)
		service.TaskRunning.Delete(commandKey)
	}()
	time.Sleep(time.Second)
	var wg sync.WaitGroup
	// 打开之前上传文件
	file, err := os.OpenFile(saveFilePath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
	defer file.Close()
	if err != nil {
		logger.Error("打开之前上传文件不存在 filePath:", saveFilePath)
		return err
	}

	// 分片大小获取
	fi, err := os.Stat(path.Join(tmpFileDir, fileName+"_0"))
	if err != nil {
		logger.Error("获取分片大小失败 tmpFilePath_0:", path.Join(tmpFileDir, fileName+"_0"))
		return err
	}
	chunkSize := fi.Size()

	//if chunkSize > 0 {
	//	return errors.New("合并失败")
	//}

	for i := 0; i < chunkTotal; i++ {
		if err := writeBlock(file, i, chunkSize, fileName, tmpFileDir, &wg); err != nil {
			logger.Error(err, "写入文件块失败 i:", i, " chunkSize:", chunkSize, " fileName:", fileName, " tmpFileDir:", tmpFileDir)
			return err
		}
		if val, ok := service.TaskRunning.Load(commandKey); ok {
			commandRunningValue = val.(structs.RunningCommand)
			commandRunningValue.Progress = fmt.Sprintf("%d/%d", i+1, chunkTotal)
			service.TaskRunning.Store(commandKey, commandRunningValue)
		}
	}
	wg.Wait()
	logger.Info("所有块写入完成 saveFilePath:", saveFilePath)
	logger.Info("所有块写入完成，开始删除临时文件夹 tmpFileDir:", tmpFileDir)

	if strings.Contains(tmpFileDir, "/mnt/user-data/store") && strings.Contains(tmpFileDir, "/chunk/") {
		if len(tmpFileDir) < 35 {
			err := errors.New("不是目标目录")
			logger.Error(err, " path:", tmpFileDir)
			return err
		}
		if err := os.RemoveAll(tmpFileDir); err != nil {
			logger.Error(err, "删除临时文件夹失败 tmpFileDir：", tmpFileDir)
			return err
		} else {
			logger.Info("已删除临时文件夹 tmpFileDir:", tmpFileDir)
		}
	}
	return nil
}
